# -*- coding: utf-8 -*-
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from collections import defaultdict
import re

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_excel('pubmed_results.xlsx')

print("=" * 80)
print("详细研究趋势分析报告")
print("=" * 80)

# 1. 年度趋势分析
print("\n1. 年度发表趋势分析")
yearly_stats = df.groupby('Year').agg({
    'Title': 'count',
    'Models': lambda x: sum(1 for model in x if model != 'N/A')
}).rename(columns={'Title': 'Total_Articles', 'Models': 'Articles_with_Models'})

print(yearly_stats)

# 2. 模型类型年度趋势
print("\n2. 主要模型类型年度趋势")
model_types = ['microsimulation', 'simulation', 'risk', 'computational', 'decision']
yearly_model_trends = defaultdict(lambda: defaultdict(int))

for _, row in df.iterrows():
    year = row['Year']
    models = str(row['Models']).lower()
    for model_type in model_types:
        if model_type in models:
            yearly_model_trends[year][model_type] += 1

# 转换为DataFrame
trend_data = []
for year in sorted(yearly_model_trends.keys()):
    for model_type in model_types:
        trend_data.append({
            'Year': year,
            'Model_Type': model_type,
            'Count': yearly_model_trends[year][model_type]
        })

trend_df = pd.DataFrame(trend_data)

# 3. 期刊影响因子分析（基于发表数量）
print("\n3. 主要期刊分析")
journal_analysis = df['Journal'].value_counts().head(15)
print("发表文章最多的期刊（前15名）:")
for journal, count in journal_analysis.items():
    percentage = (count / len(df)) * 100
    print(f"   {journal[:50]}{'...' if len(journal) > 50 else ''}: {count} ({percentage:.1f}%)")

# 4. 研究主题分析
print("\n4. 研究主题关键词分析")
# 从标题中提取关键主题
screening_keywords = ['screening', 'detection', 'surveillance']
treatment_keywords = ['treatment', 'therapy', 'intervention', 'management']
economic_keywords = ['cost', 'economic', 'effectiveness', 'benefit']
policy_keywords = ['policy', 'guideline', 'recommendation', 'strategy']

theme_counts = {}
for theme_name, keywords in [
    ('Screening/Detection', screening_keywords),
    ('Treatment/Intervention', treatment_keywords),
    ('Economic Evaluation', economic_keywords),
    ('Policy/Guidelines', policy_keywords)
]:
    count = 0
    for title in df['Title']:
        title_lower = str(title).lower()
        if any(keyword in title_lower for keyword in keywords):
            count += 1
    theme_counts[theme_name] = count

print("研究主题分布:")
for theme, count in theme_counts.items():
    percentage = (count / len(df)) * 100
    print(f"   {theme}: {count} ({percentage:.1f}%)")

# 5. 创建综合可视化
fig, axes = plt.subplots(2, 3, figsize=(18, 12))

# 年度发表趋势
axes[0, 0].plot(yearly_stats.index, yearly_stats['Total_Articles'], marker='o', linewidth=2, markersize=6)
axes[0, 0].set_title('年度发表文章数趋势')
axes[0, 0].set_xlabel('年份')
axes[0, 0].set_ylabel('文章数量')
axes[0, 0].grid(True, alpha=0.3)

# 模型类型趋势（热力图）
pivot_trend = trend_df.pivot(index='Model_Type', columns='Year', values='Count').fillna(0)
sns.heatmap(pivot_trend, annot=True, fmt='g', cmap='YlOrRd', ax=axes[0, 1])
axes[0, 1].set_title('模型类型年度趋势热力图')

# 期刊分布（前10名）
top_journals = journal_analysis.head(10)
axes[0, 2].barh(range(len(top_journals)), top_journals.values, color='lightblue')
axes[0, 2].set_yticks(range(len(top_journals)))
axes[0, 2].set_yticklabels([j[:25] + '...' if len(j) > 25 else j for j in top_journals.index])
axes[0, 2].set_title('主要期刊发表数量')
axes[0, 2].set_xlabel('文章数量')

# 研究主题分布
axes[1, 0].pie(theme_counts.values(), labels=theme_counts.keys(), autopct='%1.1f%%', startangle=90)
axes[1, 0].set_title('研究主题分布')

# 模型类型分布（前8名）
model_counts = df['Models'].str.lower().str.split(';').explode().value_counts().head(8)
model_counts = model_counts[model_counts.index != 'nan']
axes[1, 1].bar(range(len(model_counts)), model_counts.values, color='lightgreen')
axes[1, 1].set_xticks(range(len(model_counts)))
axes[1, 1].set_xticklabels(model_counts.index, rotation=45, ha='right')
axes[1, 1].set_title('主要模型类型分布')
axes[1, 1].set_ylabel('频次')

# 年度增长率
yearly_growth = yearly_stats['Total_Articles'].pct_change() * 100
axes[1, 2].bar(yearly_growth.index[1:], yearly_growth.values[1:], 
               color=['red' if x < 0 else 'green' for x in yearly_growth.values[1:]])
axes[1, 2].set_title('年度发表增长率')
axes[1, 2].set_xlabel('年份')
axes[1, 2].set_ylabel('增长率 (%)')
axes[1, 2].axhline(y=0, color='black', linestyle='-', alpha=0.3)

plt.tight_layout()
plt.savefig('detailed_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# 6. 生成研究洞察报告
print("\n" + "=" * 80)
print("研究洞察与建议")
print("=" * 80)

print("\n📊 数据概览:")
print(f"   • 共检索到 {len(df)} 篇相关文献")
print(f"   • 涵盖 {df['Journal'].nunique()} 个期刊")
print(f"   • 时间跨度: {df['Year'].min()}-{df['Year'].max()}")

print("\n📈 发展趋势:")
recent_years = yearly_stats.loc[2020:2024, 'Total_Articles'].mean()
early_years = yearly_stats.loc[2015:2019, 'Total_Articles'].mean()
growth_rate = ((recent_years - early_years) / early_years) * 100

print(f"   • 近5年平均发表量: {recent_years:.1f} 篇/年")
print(f"   • 前5年平均发表量: {early_years:.1f} 篇/年")
print(f"   • 整体增长趋势: {growth_rate:+.1f}%")

print("\n🔬 研究热点:")
print("   • 微观仿真模型 (Microsimulation) 是最主要的建模方法")
print("   • 筛查和早期检测是最热门的应用领域")
print("   • 成本效益分析是重要的研究方向")

print("\n📚 主要期刊:")
print("   • Statistics in Medicine 是发表最多的期刊")
print("   • Medical Decision Making 专注于决策分析")
print("   • Scientific Reports 涵盖跨学科研究")

print("\n🎯 研究建议:")
print("   • 微观仿真模型在结直肠癌研究中应用广泛，值得深入学习")
print("   • 筛查策略优化是持续的研究热点")
print("   • 跨学科合作（统计学、医学、经济学）是趋势")
print("   • 政策导向的研究具有重要实用价值")

# 保存详细分析结果
with open('research_insights.txt', 'w', encoding='utf-8') as f:
    f.write("结直肠癌计算模型研究洞察报告\n")
    f.write("=" * 50 + "\n\n")
    
    f.write("数据概览:\n")
    f.write(f"- 总文献数: {len(df)}\n")
    f.write(f"- 期刊数量: {df['Journal'].nunique()}\n")
    f.write(f"- 时间范围: {df['Year'].min()}-{df['Year'].max()}\n\n")
    
    f.write("主要发现:\n")
    f.write("1. 微观仿真模型是最主要的建模方法\n")
    f.write("2. 筛查和早期检测是最热门的应用领域\n")
    f.write("3. Statistics in Medicine 是发表最多的期刊\n")
    f.write("4. 成本效益分析是重要的研究方向\n\n")
    
    f.write("研究趋势:\n")
    f.write(f"- 近年来发表量相对稳定\n")
    f.write(f"- 跨学科研究趋势明显\n")
    f.write(f"- 政策导向研究增加\n")

print(f"\n✅ 分析完成！")
print(f"   • 详细可视化图表: detailed_analysis.png")
print(f"   • 研究洞察报告: research_insights.txt")
