# -*- coding: utf-8 -*-
from Bio import Entrez
import pandas as pd
import re

# 设置 Entrez 工具的参数
Entrez.email = "<EMAIL>"  # 替换为你的邮箱
# Entrez.api_key = "your_api_key"  # 可选，如果有 API 密钥

# PubMed 检索式
query = (
    '("Colorectal Neoplasms"[Mesh] OR ("colorectal cancer"[TIAB] OR "colorectal neoplasm"[TIAB] OR "colorectal tumor"[TIAB] OR "colorectal tumour"[TIAB] OR "colorectal malignan*"[TIAB] OR "colon cancer"[TIAB] OR "colon neoplasm"[TIAB] OR "colon tumor"[TIAB] OR "colon tumour"[TIAB] OR "colon malignan*"[TIAB] OR "rectal cancer"[TIAB] OR "rectal neoplasm"[TIAB] OR "rectal tumor"[TIAB] OR "rectal tumour"[TIAB] OR "rectal malignan*"[TIAB])) '
    'AND ("Computer Simulation"[Mesh] OR ("microsimulation model*"[TIAB] OR "computational model*"[TIAB] OR "individual-based model*"[TIAB] OR "silico model"[TIAB] OR "computer simulation"[TIAB])) '
    'NOT ("cellular"[TIAB] OR "intracellular"[TIAB] OR "cell-level"[TIAB] OR "molecular"[TIAB] OR "subcellular"[TIAB]) '
    'AND ( "Mass Screening"[Mesh] OR "Early Detection of Cancer"[Mesh] OR "Intervention Studies"[Mesh] OR "Therapeutics"[Mesh] OR "Patient Care Management"[Mesh] OR "Follow-Up Studies"[Mesh] OR ("screening"[TIAB] OR "early detection"[TIAB] OR "intervention"[TIAB] OR "treatment"[TIAB] OR "therapy"[TIAB] OR "follow-up"[TIAB] OR "management"[TIAB]))'
    'AND ("2015/01/01"[Date - Publication] : "2025/03/31"[Date - Publication])'
)

# 使用 Entrez 检索 PubMed
handle = Entrez.esearch(db="pubmed", term=query, retmax=1000)  # retmax 是最大返回结果数
record = Entrez.read(handle)
handle.close()

# 获取检索结果的 ID 列表
id_list = record["IdList"]

# 初始化列表存储数据
data = []

# 定义模型名称的正则表达式
# model_pattern = re.compile(
#    r"\b(microsimulation model\w*|computational model\w*|individual-based model\w*|"
#     r"agent-based model\w*|simulation model\w*|mathematical model\w*|"
#     r"statistical model\w*|predictive model\w*|risk model\w*|"
#     r"model\w*|"
#     r"Markov model\w*|decision model\w*|dynamic model\w*)\b",
#     re.IGNORECASE
# )


model_pattern = re.compile(
    r"\b(?:microsimulation|computational|individual-based|agent-based|simulation|mathematical|"
    r"statistical|predictive|risk|Markov|decision|dynamic model\w*)\b", re.IGNORECASE
)

# 批量获取每篇文章的详细信息
batch_size = 100  # 每次请求的文章数量
for start in range(0, len(id_list), batch_size):
    end = min(start + batch_size, len(id_list))
    batch_ids = id_list[start:end]
    
    # 获取文章详细信息
    handle = Entrez.efetch(db="pubmed", id=",".join(batch_ids), rettype="abstract", retmode="xml")
    articles = Entrez.read(handle)
    handle.close()
    
    # 解析每篇文章
    for article in articles["PubmedArticle"]:
        # 提取标题
        title = article["MedlineCitation"]["Article"]["ArticleTitle"]
        
        # 提取作者
        authors = []
        if "AuthorList" in article["MedlineCitation"]["Article"]:
            for author in article["MedlineCitation"]["Article"]["AuthorList"]:
                if "LastName" in author and "ForeName" in author:
                    authors.append(f"{author['LastName']} {author['ForeName']}")
        authors = ", ".join(authors)
        
        # 提取发表年份
        year = article["MedlineCitation"]["Article"]["Journal"]["JournalIssue"]["PubDate"].get("Year", "N/A")
        
        # 提取杂志名称
        journal = article["MedlineCitation"]["Article"]["Journal"]["Title"]
        
        # 提取卷（Volume）
        volume = article["MedlineCitation"]["Article"]["Journal"]["JournalIssue"].get("Volume", "N/A")
        
        # 提取期（Issue）
        issue = article["MedlineCitation"]["Article"]["Journal"]["JournalIssue"].get("Issue", "N/A")
        
        # 提取起止页码
        pages = article["MedlineCitation"]["Article"].get("Pagination", {}).get("MedlinePgn", "N/A")
        
        # 提取摘要
        abstract = "N/A"
        if "Abstract" in article["MedlineCitation"]["Article"]:
            abstract = " ".join(article["MedlineCitation"]["Article"]["Abstract"]["AbstractText"])
        
        # 提取 DOI
        doi = "N/A"
        if "ELocationID" in article["MedlineCitation"]["Article"]:
            for elocation in article["MedlineCitation"]["Article"]["ELocationID"]:
                if elocation.attributes.get("EIdType", "") == "doi":
                    doi = elocation
                    break
        
        # 提取 URL
        url = f"https://pubmed.ncbi.nlm.nih.gov/{article['MedlineCitation']['PMID']}/"
        
        # 提取模型名称
        models = set()
        # 从标题中提取
        title_models = model_pattern.findall(title)
        models.update(title_models)
        # 从摘要中提取
        abstract_models = model_pattern.findall(abstract)
        models.update(abstract_models)
        # 将模型名称列表转换为字符串
        models_str = "; ".join(models) if models else "N/A"
        
        # 将数据添加到列表中
        data.append({
            "Title": title,
            "Authors": authors,
            "Year": year,
            "Journal": journal,
            "Volume": volume,
            "Issue": issue,
            "Pages": pages,
            "Abstract": abstract,
            "DOI": doi,
            "URL": url,
            "Models": models_str
        })

# 将数据转换为 DataFrame
df = pd.DataFrame(data)

# 保存到 Excel 文件
df.to_excel("pubmed_results.xlsx", index=False)

print(f"检索到 {len(data)} 篇文章，数据已保存到 pubmed_results.xlsx")