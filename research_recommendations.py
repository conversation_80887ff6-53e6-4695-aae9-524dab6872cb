# -*- coding: utf-8 -*-
import pandas as pd
import re
from collections import Counter, defaultdict

# 读取数据
df = pd.read_excel('pubmed_results.xlsx')

print("=" * 80)
print("研究推荐与文献精选分析")
print("=" * 80)

# 1. 识别高质量期刊的文章
high_impact_journals = [
    'Statistics in medicine',
    'Medical decision making',
    'Gastroenterology',
    'Cancer',
    'Biometrics',
    'Statistical methods in medical research'
]

print("\n1. 高影响因子期刊文章分析")
high_impact_articles = df[df['Journal'].isin(high_impact_journals)]
print(f"高影响因子期刊文章数: {len(high_impact_articles)} ({len(high_impact_articles)/len(df)*100:.1f}%)")

# 按期刊分组显示
for journal in high_impact_journals:
    journal_articles = df[df['Journal'] == journal]
    if len(journal_articles) > 0:
        print(f"\n{journal} ({len(journal_articles)} 篇):")
        recent_articles = journal_articles.nlargest(3, 'Year')
        for _, article in recent_articles.iterrows():
            print(f"   • {article['Year']} - {article['Title'][:80]}...")

# 2. 识别关键研究主题组合
print("\n\n2. 关键研究主题组合分析")

# 定义主题关键词
themes = {
    'microsimulation': ['microsimulation'],
    'screening': ['screening', 'detection'],
    'cost_effectiveness': ['cost', 'effectiveness', 'economic'],
    'policy': ['policy', 'guideline', 'recommendation'],
    'treatment': ['treatment', 'therapy', 'intervention'],
    'risk_prediction': ['risk', 'prediction', 'predictive'],
    'markov': ['markov'],
    'decision_analysis': ['decision']
}

# 计算主题组合
theme_combinations = defaultdict(int)
for _, row in df.iterrows():
    title_abstract = (str(row['Title']) + ' ' + str(row['Abstract'])).lower()
    present_themes = []
    
    for theme, keywords in themes.items():
        if any(keyword in title_abstract for keyword in keywords):
            present_themes.append(theme)
    
    if len(present_themes) >= 2:
        combination = ' + '.join(sorted(present_themes))
        theme_combinations[combination] += 1

print("主要研究主题组合（前10名）:")
for combination, count in sorted(theme_combinations.items(), key=lambda x: x[1], reverse=True)[:10]:
    print(f"   {combination}: {count} 篇")

# 3. 识别研究空白和机会
print("\n\n3. 研究空白与机会分析")

# 分析较少研究的组合
underresearched_areas = []
important_combinations = [
    'microsimulation + policy',
    'markov + screening',
    'decision_analysis + treatment',
    'risk_prediction + policy',
    'cost_effectiveness + treatment'
]

print("潜在研究空白领域:")
for combo in important_combinations:
    count = theme_combinations.get(combo, 0)
    if count < 5:
        underresearched_areas.append((combo, count))
        print(f"   • {combo}: 仅 {count} 篇研究")

# 4. 推荐必读文章（基于关键词密度和期刊质量）
print("\n\n4. 推荐必读文章")

def calculate_relevance_score(row):
    """计算文章相关性得分"""
    score = 0
    title_abstract = (str(row['Title']) + ' ' + str(row['Abstract'])).lower()
    
    # 期刊权重
    if row['Journal'] in high_impact_journals:
        score += 3
    
    # 关键词权重
    key_terms = {
        'microsimulation': 3,
        'cost-effectiveness': 2,
        'screening': 2,
        'decision': 2,
        'markov': 2,
        'policy': 1,
        'guideline': 1
    }
    
    for term, weight in key_terms.items():
        if term in title_abstract:
            score += weight
    
    # 年份权重（近期文章加分）
    if row['Year'] >= 2020:
        score += 2
    elif row['Year'] >= 2018:
        score += 1
    
    return score

# 计算相关性得分
df['relevance_score'] = df.apply(calculate_relevance_score, axis=1)

# 推荐文章
recommended_articles = df.nlargest(15, 'relevance_score')

print("基于相关性得分的推荐文章（前15篇）:")
for i, (_, article) in enumerate(recommended_articles.iterrows(), 1):
    print(f"\n{i}. 【{article['Year']}】{article['Title']}")
    print(f"   期刊: {article['Journal']}")
    print(f"   模型类型: {article['Models']}")
    print(f"   相关性得分: {article['relevance_score']}")
    if len(str(article['Abstract'])) > 100:
        print(f"   摘要: {str(article['Abstract'])[:150]}...")

# 5. 研究方向建议
print("\n\n5. 未来研究方向建议")

research_suggestions = [
    "🔬 方法学创新",
    "   • 结合机器学习的微观仿真模型",
    "   • 多状态马尔可夫模型的改进",
    "   • 贝叶斯网络在风险预测中的应用",
    "",
    "🎯 应用领域拓展", 
    "   • 个性化筛查策略设计",
    "   • 精准医疗的成本效益分析",
    "   • 人工智能辅助诊断的经济评估",
    "",
    "📊 政策研究加强",
    "   • 筛查指南的循证更新",
    "   • 医保政策的建模分析",
    "   • 国际比较研究",
    "",
    "🌐 跨学科合作",
    "   • 统计学 + 临床医学 + 卫生经济学",
    "   • 计算机科学 + 流行病学",
    "   • 行为科学 + 决策分析"
]

for suggestion in research_suggestions:
    print(suggestion)

# 6. 保存推荐结果
with open('research_recommendations.txt', 'w', encoding='utf-8') as f:
    f.write("结直肠癌计算模型研究推荐报告\n")
    f.write("=" * 50 + "\n\n")
    
    f.write("高影响因子期刊推荐:\n")
    for journal in high_impact_journals:
        count = len(df[df['Journal'] == journal])
        if count > 0:
            f.write(f"- {journal}: {count} 篇\n")
    
    f.write(f"\n研究空白领域:\n")
    for combo, count in underresearched_areas:
        f.write(f"- {combo}: {count} 篇研究\n")
    
    f.write(f"\n推荐必读文章（前10篇）:\n")
    for i, (_, article) in enumerate(recommended_articles.head(10).iterrows(), 1):
        f.write(f"{i}. [{article['Year']}] {article['Title']}\n")
        f.write(f"   期刊: {article['Journal']}\n")
        f.write(f"   URL: {article['URL']}\n\n")

print(f"\n✅ 推荐分析完成！")
print(f"   • 详细推荐报告已保存: research_recommendations.txt")
print(f"   • 可结合之前的分析图表进行综合研究规划")
