# -*- coding: utf-8 -*-
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import re
import numpy as np
from wordcloud import WordCloud
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_excel('pubmed_results.xlsx')

print("=" * 60)
print("PubMed 搜索结果分析报告")
print("=" * 60)

# 1. 基本统计信息
print(f"\n1. 基本统计信息")
print(f"   总文章数: {len(df)}")
print(f"   时间范围: {df['Year'].min()} - {df['Year'].max()}")
print(f"   期刊数量: {df['Journal'].nunique()}")
print(f"   有模型信息的文章: {len(df[df['Models'] != 'N/A'])}")

# 2. 年份分布分析
print(f"\n2. 年份分布分析")
year_counts = df['Year'].value_counts().sort_index()
print(year_counts)

# 3. 期刊分布分析（前10名）
print(f"\n3. 期刊分布分析（前10名）")
journal_counts = df['Journal'].value_counts().head(10)
print(journal_counts)

# 4. 模型类型分析
print(f"\n4. 模型类型分析")
model_list = []
for models in df['Models']:
    if models != 'N/A':
        # 分割模型名称并清理
        model_names = [m.strip().lower() for m in str(models).split(';')]
        model_list.extend(model_names)

model_counter = Counter(model_list)
print("模型类型频次（前15名）:")
for model, count in model_counter.most_common(15):
    print(f"   {model}: {count}")

# 5. 关键词分析（从标题中提取）
print(f"\n5. 标题关键词分析")
all_titles = ' '.join(df['Title'].astype(str))
# 移除常见停用词并提取关键词
common_words = ['the', 'and', 'or', 'of', 'in', 'for', 'with', 'a', 'an', 'to', 'is', 'are', 'was', 'were']
title_words = re.findall(r'\b[a-zA-Z]{4,}\b', all_titles.lower())
title_words = [word for word in title_words if word not in common_words]
title_counter = Counter(title_words)
print("标题关键词频次（前15名）:")
for word, count in title_counter.most_common(15):
    print(f"   {word}: {count}")

# 6. 创建可视化图表
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 年份分布图
axes[0, 0].bar(year_counts.index, year_counts.values, color='skyblue')
axes[0, 0].set_title('文章发表年份分布')
axes[0, 0].set_xlabel('年份')
axes[0, 0].set_ylabel('文章数量')
axes[0, 0].tick_params(axis='x', rotation=45)

# 期刊分布图（前10名）
journal_top10 = journal_counts.head(10)
axes[0, 1].barh(range(len(journal_top10)), journal_top10.values, color='lightcoral')
axes[0, 1].set_yticks(range(len(journal_top10)))
axes[0, 1].set_yticklabels([j[:30] + '...' if len(j) > 30 else j for j in journal_top10.index])
axes[0, 1].set_title('期刊发表数量（前10名）')
axes[0, 1].set_xlabel('文章数量')

# 模型类型分布图（前10名）
model_top10 = dict(model_counter.most_common(10))
if model_top10:
    axes[1, 0].bar(range(len(model_top10)), list(model_top10.values()), color='lightgreen')
    axes[1, 0].set_xticks(range(len(model_top10)))
    axes[1, 0].set_xticklabels(list(model_top10.keys()), rotation=45, ha='right')
    axes[1, 0].set_title('模型类型分布（前10名）')
    axes[1, 0].set_ylabel('频次')

# 关键词分布图（前10名）
keyword_top10 = dict(title_counter.most_common(10))
axes[1, 1].bar(range(len(keyword_top10)), list(keyword_top10.values()), color='gold')
axes[1, 1].set_xticks(range(len(keyword_top10)))
axes[1, 1].set_xticklabels(list(keyword_top10.keys()), rotation=45, ha='right')
axes[1, 1].set_title('标题关键词分布（前10名）')
axes[1, 1].set_ylabel('频次')

plt.tight_layout()
plt.savefig('pubmed_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# 7. 详细的模型分类分析
print(f"\n7. 详细模型分类分析")
model_categories = {
    'microsimulation': ['microsimulation'],
    'computational': ['computational'],
    'mathematical': ['mathematical'],
    'statistical': ['statistical'],
    'predictive': ['predictive'],
    'markov': ['markov'],
    'simulation': ['simulation'],
    'risk': ['risk'],
    'decision': ['decision']
}

category_counts = {}
for category, keywords in model_categories.items():
    count = 0
    for models in df['Models']:
        if models != 'N/A':
            models_lower = str(models).lower()
            if any(keyword in models_lower for keyword in keywords):
                count += 1
    category_counts[category] = count

print("模型类别统计:")
for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
    print(f"   {category.capitalize()}: {count}")

# 8. 保存详细分析结果
analysis_results = {
    'total_articles': len(df),
    'year_range': f"{df['Year'].min()}-{df['Year'].max()}",
    'unique_journals': df['Journal'].nunique(),
    'articles_with_models': len(df[df['Models'] != 'N/A']),
    'top_journals': journal_counts.head(5).to_dict(),
    'top_models': dict(model_counter.most_common(10)),
    'top_keywords': dict(title_counter.most_common(10)),
    'model_categories': category_counts
}

# 保存到文件
with open('analysis_summary.txt', 'w', encoding='utf-8') as f:
    f.write("PubMed 搜索结果分析摘要\n")
    f.write("=" * 40 + "\n\n")
    f.write(f"总文章数: {analysis_results['total_articles']}\n")
    f.write(f"时间范围: {analysis_results['year_range']}\n")
    f.write(f"期刊数量: {analysis_results['unique_journals']}\n")
    f.write(f"有模型信息的文章: {analysis_results['articles_with_models']}\n\n")
    
    f.write("前5名期刊:\n")
    for journal, count in analysis_results['top_journals'].items():
        f.write(f"  {journal}: {count}\n")
    
    f.write("\n前10名模型类型:\n")
    for model, count in analysis_results['top_models'].items():
        f.write(f"  {model}: {count}\n")
    
    f.write("\n模型类别统计:\n")
    for category, count in sorted(analysis_results['model_categories'].items(), 
                                  key=lambda x: x[1], reverse=True):
        f.write(f"  {category.capitalize()}: {count}\n")

print(f"\n分析完成！")
print(f"- 可视化图表已保存为: pubmed_analysis.png")
print(f"- 分析摘要已保存为: analysis_summary.txt")
